<template>
  <div>
    <!-- SKU管理对话框 -->
    <el-dialog
      title="SKU管理"
      :visible.sync="dialogVisible"
      width="1200px"
      append-to-body
      @close="handleClose"
    >
      <div style="margin-bottom: 16px">
        <el-button type="primary" size="mini" @click="handleAddSku">
          添加SKU
        </el-button>
      </div>

      <el-table :data="skuList" style="width: 100%">
        <el-table-column prop="skuName" label="SKU名称" width="150" />
        <el-table-column label="原价">
          <template slot-scope="scope">
            ¥{{ formatPrice(scope.row.originalPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="售价">
          <template slot-scope="scope">
            ¥{{ formatPrice(scope.row.salePrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" />
        <el-table-column prop="salesCount" label="销量" />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.status === 1 ? 'success' : 'danger'"
              size="mini"
            >
              {{ scope.row.status === 1 ? "上架" : "下架" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="关联课程" min-width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.courses && scope.row.courses.length > 0">
              <el-tag
                v-for="course in scope.row.courses"
                :key="course.id"
                type="info"
                size="mini"
                style="margin-right: 4px; margin-bottom: 2px"
              >
                {{ course.name }}
              </el-tag>
            </div>
            <span v-else style="color: #999">暂无关联课程</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleEditSku(scope.row, scope.$index)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleManageSkuCourses(scope.row, scope.$index)"
              style="color: #409eff"
            >
              管理课程
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleDeleteSku(scope.$index)"
              style="color: #f56c6c"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- SKU编辑组件 -->
    <SkuEdit
      :visible.sync="skuEditVisible"
      :sku-data="currentSkuData"
      :is-edit="isEditMode"
      @save="handleSkuSave"
    />

    <!-- 课程关联管理组件 -->
    <CourseRelationManagement
      :visible.sync="courseRelationVisible"
      :selected-courses="selectedCourses"
      :available-courses="availableCourses"
      @save="handleCourseRelationSave"
    />
  </div>
</template>

<script>
import {
  createWbclassCourseProductSku,
  deleteWbclassCourseProductSku,
  getWbclassCourseProductSkuList,
  manageSkuCourseRelations,
  updateWbclassCourseProductSku,
} from "@/api/wbclass/courseProduct";
import CourseRelationManagement from "./CourseRelationManagement.vue";
import SkuEdit from "./SkuEdit.vue";

export default {
  name: "SkuManagement",
  components: {
    SkuEdit,
    CourseRelationManagement,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    productId: {
      type: Number,
      required: true,
    },
    availableCourses: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      skuList: [],
      skuEditVisible: false,
      courseRelationVisible: false,
      currentSkuData: {},
      currentSkuIndex: -1,
      isEditMode: false,
      selectedCourses: [],
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.loadSkuData();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit("update:visible", false);
      }
    },
  },
  methods: {
    /** 加载SKU数据 */
    async loadSkuData() {
      try {
        // 使用专门的SKU列表API，这个API会返回包含关联课程信息的SKU数据
        const response = await getWbclassCourseProductSkuList(this.productId);
        this.skuList = response.data || [];

        // 确保每个SKU都有courses属性
        for (let sku of this.skuList) {
          if (!sku.courses) {
            sku.courses = [];
          }
        }
      } catch (error) {
        this.$message.error("获取SKU信息失败");
        this.skuList = [];
      }
    },

    /** 价格格式化 */
    formatPrice(price) {
      if (!price || price === 0) return "0.00";
      return (price / 100).toFixed(2);
    },

    /** 添加SKU */
    handleAddSku() {
      this.currentSkuData = {
        id: null,
        skuName: "",
        originalPrice: 0,
        salePrice: 0,
        stock: 999,
        status: 1,
        specInfo: "",
        courses: [],
      };
      this.isEditMode = false;
      this.skuEditVisible = true;
    },

    /** 编辑SKU */
    handleEditSku(sku, index) {
      this.currentSkuData = { ...sku };
      this.currentSkuIndex = index;
      this.isEditMode = true;
      this.skuEditVisible = true;
    },

    /** 管理SKU关联课程 */
    handleManageSkuCourses(sku, index) {
      this.currentSkuIndex = index;
      this.selectedCourses = sku.courses ? [...sku.courses] : [];
      this.courseRelationVisible = true;
    },

    /** 删除SKU */
    async handleDeleteSku(index) {
      const sku = this.skuList[index];
      this.$modal
        .confirm('是否确认删除SKU"' + sku.skuName + '"?')
        .then(async () => {
          try {
            await deleteWbclassCourseProductSku(sku.id);
            await this.loadSkuData();
            this.$message.success("删除成功");
          } catch (error) {
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },

    /** SKU保存处理 */
    async handleSkuSave(skuData) {
      try {
        if (this.isEditMode) {
          // 编辑现有SKU
          const updateData = {
            id: skuData.id,
            ...skuData,
            courseIds: [],
          };
          await updateWbclassCourseProductSku(updateData);
          this.$message.success("修改成功");
        } else {
          // 新增SKU
          const createData = {
            productId: this.productId,
            ...skuData,
            courseIds: [],
          };
          await createWbclassCourseProductSku(createData);
          this.$message.success("添加成功");
        }

        await this.loadSkuData();
        this.skuEditVisible = false;
      } catch (error) {
        this.$message.error(this.isEditMode ? "修改失败" : "添加失败");
      }
    },

    /** 课程关联保存处理 */
    async handleCourseRelationSave(selectedCourses) {
      if (this.currentSkuIndex >= 0) {
        try {
          const sku = this.skuList[this.currentSkuIndex];
          const courseIds = selectedCourses.map((c) => c.id);

          await manageSkuCourseRelations({
            skuId: sku.id,
            courseIds: courseIds,
          });

          // 更新本地数据
          this.$set(this.skuList[this.currentSkuIndex], "courses", [
            ...selectedCourses,
          ]);

          this.$message.success("关联课程保存成功");
        } catch (error) {
          this.$message.error("保存失败");
        }
      }
      this.courseRelationVisible = false;
    },

    /** 关闭对话框 */
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>
