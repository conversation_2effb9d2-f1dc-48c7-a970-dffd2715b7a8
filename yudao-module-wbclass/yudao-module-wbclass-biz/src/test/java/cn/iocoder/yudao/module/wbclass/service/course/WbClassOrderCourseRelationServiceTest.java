package cn.iocoder.yudao.module.wbclass.service.course;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseOrderDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassOrderCourseRelationDO;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseOrderMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassOrderCourseRelationMapper;
import cn.iocoder.yudao.module.wbclass.enums.PayStatusEnum;
import cn.iocoder.yudao.module.wbclass.enums.RefundStatusEnum;
import cn.iocoder.yudao.module.wbclass.service.product.WbClassCourseProductSkuService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * {@link WbClassOrderCourseRelationService} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(WbClassOrderCourseRelationServiceImpl.class)
public class WbClassOrderCourseRelationServiceTest extends BaseDbUnitTest {

    @Resource
    private WbClassOrderCourseRelationService orderCourseRelationService;

    @Resource
    private WbClassOrderCourseRelationMapper relationMapper;

    @Resource
    private WbClassCourseMapper courseMapper;

    @Resource
    private WbClassCourseOrderMapper orderMapper;

    @MockBean
    private WbClassCourseProductSkuService skuService;

    @Test
    public void testCreateOrderCourseRelation() {
        // 准备参数
        Long orderId = 1L;
        Long courseId = 100L;
        String courseName = "Java基础课程";
        Integer sort = 0;

        // 调用
        orderCourseRelationService.createOrderCourseRelation(orderId, courseId, courseName, sort);

        // 断言
        List<WbClassOrderCourseRelationDO> relations = relationMapper.selectListByOrderId(orderId);
        assertEquals(1, relations.size());
        
        WbClassOrderCourseRelationDO relation = relations.get(0);
        assertEquals(orderId, relation.getOrderId());
        assertEquals(courseId, relation.getCourseId());
        assertEquals(courseName, relation.getCourseName());
        assertEquals(sort, relation.getSort());
        assertEquals(1, relation.getStatus());
    }

    @Test
    public void testCreateOrderCourseRelations() {
        // 准备参数
        Long orderId = 2L;
        Long skuId = 200L;
        
        // Mock SKU关联的课程
        List<WbClassCourseDO> mockCourses = Arrays.asList(
            WbClassCourseDO.builder().id(101L).name("Java基础").build(),
            WbClassCourseDO.builder().id(102L).name("Java进阶").build()
        );
        when(skuService.getCoursesBySkuId(skuId)).thenReturn(mockCourses);

        // 调用
        orderCourseRelationService.createOrderCourseRelations(orderId, skuId);

        // 断言
        List<WbClassOrderCourseRelationDO> relations = relationMapper.selectListByOrderId(orderId);
        assertEquals(2, relations.size());
        
        // 验证第一个关联
        WbClassOrderCourseRelationDO relation1 = relations.get(0);
        assertEquals(orderId, relation1.getOrderId());
        assertEquals(101L, relation1.getCourseId());
        assertEquals("Java基础", relation1.getCourseName());
        assertEquals(0, relation1.getSort());
        
        // 验证第二个关联
        WbClassOrderCourseRelationDO relation2 = relations.get(1);
        assertEquals(orderId, relation2.getOrderId());
        assertEquals(102L, relation2.getCourseId());
        assertEquals("Java进阶", relation2.getCourseName());
        assertEquals(1, relation2.getSort());
    }

    @Test
    public void testGetCoursesByOrderId() {
        // 准备数据
        Long orderId = 3L;
        
        // 创建课程
        WbClassCourseDO course1 = WbClassCourseDO.builder()
                .name("Python基础")
                .status(1)
                .build();
        courseMapper.insert(course1);
        
        WbClassCourseDO course2 = WbClassCourseDO.builder()
                .name("Python进阶")
                .status(1)
                .build();
        courseMapper.insert(course2);
        
        // 创建关联关系
        WbClassOrderCourseRelationDO relation1 = WbClassOrderCourseRelationDO.builder()
                .orderId(orderId)
                .courseId(course1.getId())
                .courseName(course1.getName())
                .sort(0)
                .status(1)
                .build();
        relationMapper.insert(relation1);
        
        WbClassOrderCourseRelationDO relation2 = WbClassOrderCourseRelationDO.builder()
                .orderId(orderId)
                .courseId(course2.getId())
                .courseName(course2.getName())
                .sort(1)
                .status(1)
                .build();
        relationMapper.insert(relation2);

        // 调用
        List<WbClassCourseDO> courses = orderCourseRelationService.getCoursesByOrderId(orderId);

        // 断言
        assertEquals(2, courses.size());
        assertEquals("Python基础", courses.get(0).getName());
        assertEquals("Python进阶", courses.get(1).getName());
    }

    @Test
    public void testGetPurchasedCoursesByUserId() {
        // 准备数据
        Long userId = 1000L;
        
        // 创建课程
        WbClassCourseDO course = WbClassCourseDO.builder()
                .name("Go语言基础")
                .status(1)
                .build();
        courseMapper.insert(course);
        
        // 创建已支付订单
        WbClassCourseOrderDO order = WbClassCourseOrderDO.builder()
                .orderNo("ORDER001")
                .userId(userId)
                .courseId(course.getId())
                .courseName(course.getName())
                .payStatus(PayStatusEnum.PAID.getStatus())
                .refundStatus(RefundStatusEnum.NO_NEED.getStatus())
                .payTime(LocalDateTime.now())
                .build();
        orderMapper.insert(order);
        
        // 创建关联关系
        WbClassOrderCourseRelationDO relation = WbClassOrderCourseRelationDO.builder()
                .orderId(order.getId())
                .courseId(course.getId())
                .courseName(course.getName())
                .sort(0)
                .status(1)
                .build();
        relationMapper.insert(relation);

        // 调用
        List<WbClassCourseDO> purchasedCourses = orderCourseRelationService.getPurchasedCoursesByUserId(userId);

        // 断言
        assertEquals(1, purchasedCourses.size());
        assertEquals("Go语言基础", purchasedCourses.get(0).getName());
    }

    @Test
    public void testHasUserPurchasedCourse() {
        // 准备数据
        Long userId = 2000L;
        Long courseId = 300L;
        
        // 创建课程
        WbClassCourseDO course = WbClassCourseDO.builder()
                .id(courseId)
                .name("Rust编程")
                .status(1)
                .build();
        courseMapper.insert(course);
        
        // 创建已支付订单
        WbClassCourseOrderDO order = WbClassCourseOrderDO.builder()
                .orderNo("ORDER002")
                .userId(userId)
                .courseId(courseId)
                .courseName(course.getName())
                .payStatus(PayStatusEnum.PAID.getStatus())
                .refundStatus(RefundStatusEnum.NO_NEED.getStatus())
                .payTime(LocalDateTime.now())
                .build();
        orderMapper.insert(order);
        
        // 创建关联关系
        WbClassOrderCourseRelationDO relation = WbClassOrderCourseRelationDO.builder()
                .orderId(order.getId())
                .courseId(courseId)
                .courseName(course.getName())
                .sort(0)
                .status(1)
                .build();
        relationMapper.insert(relation);

        // 调用并断言
        assertTrue(orderCourseRelationService.hasUserPurchasedCourse(userId, courseId));
        assertFalse(orderCourseRelationService.hasUserPurchasedCourse(userId, 999L)); // 未购买的课程
        assertFalse(orderCourseRelationService.hasUserPurchasedCourse(9999L, courseId)); // 其他用户
    }

    @Test
    public void testDeleteRelationsByOrderId() {
        // 准备数据
        Long orderId = 4L;
        Long courseId = 400L;
        
        // 创建关联关系
        WbClassOrderCourseRelationDO relation = WbClassOrderCourseRelationDO.builder()
                .orderId(orderId)
                .courseId(courseId)
                .courseName("测试课程")
                .sort(0)
                .status(1)
                .build();
        relationMapper.insert(relation);

        // 验证关联存在
        List<WbClassOrderCourseRelationDO> relations = relationMapper.selectListByOrderId(orderId);
        assertEquals(1, relations.size());

        // 调用删除
        orderCourseRelationService.deleteRelationsByOrderId(orderId);

        // 验证关联已删除（软删除）
        relations = relationMapper.selectListByOrderId(orderId);
        assertEquals(0, relations.size());
    }

}
