package cn.iocoder.yudao.module.wbclass.service.course;

import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassOrderCourseRelationDO;

import java.util.Collection;
import java.util.List;

/**
 * 订单课程关联 Service 接口
 * 
 * 提供订单与课程关联关系的业务逻辑，解耦订单表与课程的直接关联
 *
 * <AUTHOR>
 */
public interface WbClassOrderCourseRelationService {

    /**
     * 为订单创建课程关联关系
     * 根据订单的SKU获取关联的课程列表，并创建订单-课程关联记录
     *
     * @param orderId 订单ID
     * @param skuId SKU ID
     */
    void createOrderCourseRelations(Long orderId, Long skuId);

    /**
     * 为订单创建单个课程关联关系（向后兼容）
     *
     * @param orderId 订单ID
     * @param courseId 课程ID
     * @param courseName 课程名称
     * @param sort 排序
     */
    void createOrderCourseRelation(Long orderId, Long courseId, String courseName, Integer sort);

    /**
     * 批量创建订单课程关联关系
     *
     * @param orderId 订单ID
     * @param courses 课程列表
     */
    void createOrderCourseRelations(Long orderId, List<WbClassCourseDO> courses);

    /**
     * 根据订单ID获取关联的课程列表
     *
     * @param orderId 订单ID
     * @return 课程列表
     */
    List<WbClassCourseDO> getCoursesByOrderId(Long orderId);

    /**
     * 根据订单ID列表获取关联的课程列表
     *
     * @param orderIds 订单ID列表
     * @return 课程列表
     */
    List<WbClassCourseDO> getCoursesByOrderIds(Collection<Long> orderIds);

    /**
     * 根据用户ID获取已购买的课程列表
     * 查询用户所有已支付且未退款的订单关联的课程
     *
     * @param userId 用户ID
     * @return 课程列表
     */
    List<WbClassCourseDO> getPurchasedCoursesByUserId(Long userId);

    /**
     * 检查用户是否已购买指定课程
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否已购买
     */
    boolean hasUserPurchasedCourse(Long userId, Long courseId);

    /**
     * 根据订单ID获取关联记录列表
     *
     * @param orderId 订单ID
     * @return 关联记录列表
     */
    List<WbClassOrderCourseRelationDO> getRelationsByOrderId(Long orderId);

    /**
     * 根据课程ID获取关联记录列表
     *
     * @param courseId 课程ID
     * @return 关联记录列表
     */
    List<WbClassOrderCourseRelationDO> getRelationsByCourseId(Long courseId);

    /**
     * 删除订单的所有课程关联关系
     *
     * @param orderId 订单ID
     */
    void deleteRelationsByOrderId(Long orderId);

    /**
     * 删除课程的所有订单关联关系
     *
     * @param courseId 课程ID
     */
    void deleteRelationsByCourseId(Long courseId);

}
