package cn.iocoder.yudao.module.wbclass.service.product;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuVO;
import cn.iocoder.yudao.module.wbclass.convert.product.WbClassCourseProductSkuConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductSkuDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseSkuRelationDO;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.product.WbClassCourseProductSkuMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.product.WbClassCourseSkuRelationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.wbclass.enums.ErrorCodeConstants.COURSE_PRODUCT_SKU_NOT_EXISTS;

/**
 * 课程商品SKU Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class WbClassCourseProductSkuServiceImpl implements WbClassCourseProductSkuService {

    @Resource
    private WbClassCourseProductSkuMapper skuMapper;

    @Resource
    private WbClassCourseSkuRelationMapper relationMapper;

    @Resource
    private WbClassCourseMapper courseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSku(WbClassCourseProductSkuCreateReqVO createReqVO) {
        // 转换并插入SKU
        WbClassCourseProductSkuDO sku = WbClassCourseProductSkuConvert.INSTANCE.convert(createReqVO);
        sku.setProductId(createReqVO.getProductId());
        
        // 设置默认值
        if (sku.getSalesCount() == null) {
            sku.setSalesCount(0);
        }
        if (sku.getStock() == null) {
            sku.setStock(999);
        }
        if (sku.getSort() == null) {
            sku.setSort(0);
        }
        
        skuMapper.insert(sku);
        
        // 保存课程关联关系
        if (CollUtil.isNotEmpty(createReqVO.getCourseIds())) {
            saveSkuCourseRelations(sku.getId(), createReqVO.getCourseIds());
        }
        
        return sku.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSku(WbClassCourseProductSkuUpdateReqVO updateReqVO) {
        // 校验存在
        validateSkuExists(updateReqVO.getId());
        
        // 更新SKU
        WbClassCourseProductSkuDO updateObj = WbClassCourseProductSkuConvert.INSTANCE.convert(updateReqVO);
        skuMapper.updateById(updateObj);
        
        // 更新课程关联关系
        relationMapper.deleteBySkuId(updateReqVO.getId());
        if (CollUtil.isNotEmpty(updateReqVO.getCourseIds())) {
            saveSkuCourseRelations(updateReqVO.getId(), updateReqVO.getCourseIds());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSku(Long id) {
        // 校验存在
        validateSkuExists(id);
        
        // 删除课程关联关系
        relationMapper.deleteBySkuId(id);
        
        // 删除SKU
        skuMapper.deleteById(id);
    }

    @Override
    public List<WbClassCourseProductSkuDO> getSkuListByProductId(Long productId) {
        return skuMapper.selectListByProductId(productId);
    }

    @Override
    public WbClassCourseProductSkuDO getSku(Long skuId) {
        return skuMapper.selectById(skuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSkuList(Long productId, List<WbClassCourseProductSkuVO> skuVOList) {
        if (CollUtil.isEmpty(skuVOList)) {
            // 如果SKU列表为空，删除该商品的所有SKU
            deleteSkusByProductId(productId);
            return;
        }

        // 获取现有的SKU列表
        List<WbClassCourseProductSkuDO> existingSkus = getSkuListByProductId(productId);
        
        // 收集新列表中的SKU ID
        List<Long> newSkuIds = skuVOList.stream()
                .map(WbClassCourseProductSkuVO::getId)
                .filter(id -> id != null && id > 0)
                .collect(Collectors.toList());

        // 删除不在新列表中的现有SKU
        for (WbClassCourseProductSkuDO existingSku : existingSkus) {
            if (!newSkuIds.contains(existingSku.getId())) {
                // 删除SKU课程关联关系
                relationMapper.deleteBySkuId(existingSku.getId());
                // 删除SKU
                skuMapper.deleteById(existingSku.getId());
            }
        }

        // 保存或更新SKU列表
        for (int i = 0; i < skuVOList.size(); i++) {
            WbClassCourseProductSkuVO skuVO = skuVOList.get(i);
            
            // 转换并保存SKU
            WbClassCourseProductSkuDO sku = WbClassCourseProductSkuConvert.INSTANCE.convert(skuVO);
            sku.setProductId(productId);
            sku.setSort(i); // 使用索引作为排序
            if (sku.getSalesCount() == null) {
                sku.setSalesCount(0);
            }
            if (sku.getStock() == null) {
                sku.setStock(999);
            }
            
            if (sku.getId() != null && sku.getId() > 0) {
                // 更新现有SKU
                skuMapper.updateById(sku);
                // 先删除该SKU的现有课程关联关系
                relationMapper.deleteBySkuId(sku.getId());
            } else {
                // 插入新SKU
                skuMapper.insert(sku);
            }
            
            // 保存SKU和课程的关联关系
            if (CollUtil.isNotEmpty(skuVO.getCourseIds())) {
                saveSkuCourseRelations(sku.getId(), skuVO.getCourseIds());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSkusByProductId(Long productId) {
        // 获取该商品的所有SKU
        List<WbClassCourseProductSkuDO> skuList = getSkuListByProductId(productId);
        
        // 删除SKU课程关联关系
        for (WbClassCourseProductSkuDO sku : skuList) {
            relationMapper.deleteBySkuId(sku.getId());
        }
        
        // 删除SKU
        skuMapper.deleteByProductId(productId);
    }

    @Override
    public List<WbClassCourseDO> getCoursesBySkuId(Long skuId) {
        // 获取SKU关联的课程ID列表
        List<WbClassCourseSkuRelationDO> relations = relationMapper.selectListBySkuId(skuId);
        if (CollUtil.isEmpty(relations)) {
            return new ArrayList<>();
        }

        List<Long> courseIds = relations.stream()
                .map(WbClassCourseSkuRelationDO::getCourseId)
                .collect(Collectors.toList());
        
        // 查询课程列表
        return courseMapper.selectBatchIds(courseIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manageSkuCourseRelations(Long skuId, List<Long> courseIds) {
        // 校验SKU存在
        validateSkuExists(skuId);
        
        // 删除现有关联关系
        relationMapper.deleteBySkuId(skuId);
        
        // 创建新的关联关系
        if (CollUtil.isNotEmpty(courseIds)) {
            saveSkuCourseRelations(skuId, courseIds);
        }
    }

    @Override
    public void incrementSalesCount(Long skuId) {
        WbClassCourseProductSkuDO sku = getSku(skuId);
        if (sku != null) {
            sku.setSalesCount(sku.getSalesCount() + 1);
            skuMapper.updateById(sku);
        }
    }

    /**
     * 保存SKU课程关联关系
     */
    private void saveSkuCourseRelations(Long skuId, List<Long> courseIds) {
        for (int j = 0; j < courseIds.size(); j++) {
            Long courseId = courseIds.get(j);
            WbClassCourseSkuRelationDO relation = WbClassCourseSkuRelationDO.builder()
                    .skuId(skuId)
                    .courseId(courseId)
                    .sort(j)
                    .build();
            relationMapper.insert(relation);
        }
    }

    /**
     * 校验SKU是否存在
     */
    private void validateSkuExists(Long id) {
        if (skuMapper.selectById(id) == null) {
            throw exception(COURSE_PRODUCT_SKU_NOT_EXISTS);
        }
    }

} 