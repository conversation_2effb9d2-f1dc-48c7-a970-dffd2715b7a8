package cn.iocoder.yudao.module.wbclass.controller.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("用户 APP - 学习数据 Response VO")
@Data
public class AppWbclassStudyRespVO {

    @ApiModelProperty(value = "学习数据ID", required = true, example = "1")
    private Long id;

    @ApiModelProperty(value = "学习数据名称", required = true, example = "我的作业")
    private String name;

    @ApiModelProperty(value = "课程类型：homework-作业，course-课程", example = "course")
    private String type;

    @ApiModelProperty(value = "已购买的SKU列表")
    private List<PurchasedSkuVO> purchasedSkus;

    @ApiModel("已购买的SKU信息")
    @Data
    public static class PurchasedSkuVO {
        
        @ApiModelProperty(value = "订单ID", example = "1024")
        private Long orderId;
        
        @ApiModelProperty(value = "SKU ID", example = "1024")
        private Long skuId;
        
        @ApiModelProperty(value = "SKU名称", example = "1-3册")
        private String skuName;

        @ApiModelProperty(value = "支付时间", example = "2024-01-01 12:00:00")
        private LocalDateTime payTime;
    }

} 