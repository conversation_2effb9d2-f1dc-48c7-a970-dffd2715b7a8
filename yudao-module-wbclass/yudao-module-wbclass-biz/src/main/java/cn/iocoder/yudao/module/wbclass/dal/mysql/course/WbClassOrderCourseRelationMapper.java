package cn.iocoder.yudao.module.wbclass.dal.mysql.course;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassOrderCourseRelationDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 订单课程关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassOrderCourseRelationMapper extends BaseMapperX<WbClassOrderCourseRelationDO> {

    /**
     * 根据订单ID查询关联的课程列表
     *
     * @param orderId 订单ID
     * @return 课程关联列表
     */
    default List<WbClassOrderCourseRelationDO> selectListByOrderId(Long orderId) {
        return selectList(new LambdaQueryWrapperX<WbClassOrderCourseRelationDO>()
                .eq(WbClassOrderCourseRelationDO::getOrderId, orderId)
                .eq(WbClassOrderCourseRelationDO::getStatus, 1) // 只查询正常状态的关联
                .orderByAsc(WbClassOrderCourseRelationDO::getSort)
                .orderByAsc(WbClassOrderCourseRelationDO::getId));
    }

    /**
     * 根据订单ID列表查询关联的课程列表
     *
     * @param orderIds 订单ID列表
     * @return 课程关联列表
     */
    default List<WbClassOrderCourseRelationDO> selectListByOrderIds(Collection<Long> orderIds) {
        return selectList(new LambdaQueryWrapperX<WbClassOrderCourseRelationDO>()
                .in(WbClassOrderCourseRelationDO::getOrderId, orderIds)
                .eq(WbClassOrderCourseRelationDO::getStatus, 1) // 只查询正常状态的关联
                .orderByAsc(WbClassOrderCourseRelationDO::getOrderId)
                .orderByAsc(WbClassOrderCourseRelationDO::getSort)
                .orderByAsc(WbClassOrderCourseRelationDO::getId));
    }

    /**
     * 根据课程ID查询关联的订单列表
     *
     * @param courseId 课程ID
     * @return 订单关联列表
     */
    default List<WbClassOrderCourseRelationDO> selectListByCourseId(Long courseId) {
        return selectList(new LambdaQueryWrapperX<WbClassOrderCourseRelationDO>()
                .eq(WbClassOrderCourseRelationDO::getCourseId, courseId)
                .eq(WbClassOrderCourseRelationDO::getStatus, 1) // 只查询正常状态的关联
                .orderByDesc(WbClassOrderCourseRelationDO::getId));
    }

    /**
     * 根据课程ID列表查询关联的订单列表
     *
     * @param courseIds 课程ID列表
     * @return 订单关联列表
     */
    default List<WbClassOrderCourseRelationDO> selectListByCourseIds(Collection<Long> courseIds) {
        return selectList(new LambdaQueryWrapperX<WbClassOrderCourseRelationDO>()
                .in(WbClassOrderCourseRelationDO::getCourseId, courseIds)
                .eq(WbClassOrderCourseRelationDO::getStatus, 1) // 只查询正常状态的关联
                .orderByDesc(WbClassOrderCourseRelationDO::getId));
    }

    /**
     * 检查订单和课程是否已存在关联关系
     *
     * @param orderId 订单ID
     * @param courseId 课程ID
     * @return 是否存在关联
     */
    default boolean existsByOrderIdAndCourseId(Long orderId, Long courseId) {
        return selectCount(new LambdaQueryWrapperX<WbClassOrderCourseRelationDO>()
                .eq(WbClassOrderCourseRelationDO::getOrderId, orderId)
                .eq(WbClassOrderCourseRelationDO::getCourseId, courseId)
                .eq(WbClassOrderCourseRelationDO::getStatus, 1)) > 0;
    }

    /**
     * 根据订单ID删除所有关联关系（软删除）
     *
     * @param orderId 订单ID
     */
    default void deleteByOrderId(Long orderId) {
        update(null, new LambdaUpdateWrapper<WbClassOrderCourseRelationDO>()
                .eq(WbClassOrderCourseRelationDO::getOrderId, orderId)
                .set(WbClassOrderCourseRelationDO::getStatus, 2)); // 2表示已删除
    }

    /**
     * 根据课程ID删除所有关联关系（软删除）
     *
     * @param courseId 课程ID
     */
    default void deleteByCourseId(Long courseId) {
        update(null, new LambdaUpdateWrapper<WbClassOrderCourseRelationDO>()
                .eq(WbClassOrderCourseRelationDO::getCourseId, courseId)
                .set(WbClassOrderCourseRelationDO::getStatus, 2)); // 2表示已删除
    }

}
