package cn.iocoder.yudao.module.wbclass.dal.mysql.product;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseSkuRelationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 课程SKU关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseSkuRelationMapper extends BaseMapperX<WbClassCourseSkuRelationDO> {

    default List<WbClassCourseSkuRelationDO> selectListBySkuId(Long skuId) {
        return selectList(new LambdaQueryWrapperX<WbClassCourseSkuRelationDO>()
                .eq(WbClassCourseSkuRelationDO::getSkuId, skuId)
                .orderByAsc(WbClassCourseSkuRelationDO::getSort)
                .orderByAsc(WbClassCourseSkuRelationDO::getId));
    }

    default List<WbClassCourseSkuRelationDO> selectListByCourseId(Long courseId) {
        return selectList(new LambdaQueryWrapperX<WbClassCourseSkuRelationDO>()
                .eq(WbClassCourseSkuRelationDO::getCourseId, courseId));
    }

    default void deleteBySkuId(Long skuId) {
        delete(new LambdaQueryWrapperX<WbClassCourseSkuRelationDO>()
                .eq(WbClassCourseSkuRelationDO::getSkuId, skuId));
    }

    default void deleteByCourseId(Long courseId) {
        delete(new LambdaQueryWrapperX<WbClassCourseSkuRelationDO>()
                .eq(WbClassCourseSkuRelationDO::getCourseId, courseId));
    }

}
