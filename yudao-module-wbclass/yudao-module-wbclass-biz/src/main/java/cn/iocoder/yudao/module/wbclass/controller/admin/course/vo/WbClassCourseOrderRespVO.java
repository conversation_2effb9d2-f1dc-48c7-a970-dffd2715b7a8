package cn.iocoder.yudao.module.wbclass.controller.admin.course.vo;

import cn.iocoder.yudao.module.member.api.user.dto.UserRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 练习营课程订单 Response VO")
@Data
public class WbClassCourseOrderRespVO {

    @ApiModelProperty(value = "订单ID", example = "1024")
    private Long id;

    @ApiModelProperty(value = "订单号", example = "ORDER20241219001")
    private String orderNo;

    @ApiModelProperty(value = "用户ID", example = "2048")
    private Long userId;

    @ApiModelProperty(value = "用户IP", example = "***********")
    private String userIp;

    @ApiModelProperty(value = "下单终端：1-PC，2-H5，3-小程序，4-APP", example = "1")
    private Integer terminal;

    @ApiModelProperty(value = "课程产品ID", example = "1024")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "Java全栈开发练习营")
    private String courseName;

    @ApiModelProperty(value = "产品ID", example = "1024")
    private Long productId;

    @ApiModelProperty(value = "SKU ID", example = "1024")
    private Long skuId;

    @ApiModelProperty(value = "SKU名称", example = "1-3册")
    private String skuName;

    @ApiModelProperty(value = "课程原价，单位：分", example = "19900")
    private Integer originalPrice;

    @ApiModelProperty(value = "优惠金额，单位：分", example = "5000")
    private Integer discountPrice;

    @ApiModelProperty(value = "实付金额，单位：分", example = "14900")
    private Integer payPrice;

    @ApiModelProperty(value = "订单状态：1-待支付，2-已支付，3-已取消，4-已退款", example = "1")
    private Integer status;

    @ApiModelProperty(value = "支付状态：1-未支付，2-已支付", example = "1")
    private Integer payStatus;

    @ApiModelProperty(value = "支付时间", example = "2024-01-01 12:00:00")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "支付订单ID", example = "1024")
    private Long payOrderId;

    @ApiModelProperty(value = "支付渠道", example = "1")
    private Integer payChannel;

    @ApiModelProperty(value = "退款状态：1-无需退款，2-退款中，3-已退款", example = "1")
    private Integer refundStatus;

    @ApiModelProperty(value = "退款金额，单位：分", example = "5000")
    private Integer refundPrice;

    @ApiModelProperty(value = "退款时间", example = "2024-01-01 12:00:00")
    private LocalDateTime refundTime;

    @ApiModelProperty(value = "退款原因", example = "用户申请退款")
    private String refundReason;

    @ApiModelProperty(value = "取消时间", example = "2024-01-01 12:00:00")
    private LocalDateTime cancelTime;

    @ApiModelProperty(value = "取消类型：1-用户取消，2-系统取消，3-超时取消", example = "1")
    private Integer cancelType;

    @ApiModelProperty(value = "用户备注", example = "请尽快安排课程")
    private String userRemark;

    @ApiModelProperty(value = "管理员备注", example = "已安排课程")
    private String adminRemark;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "用户信息")
    private UserRespDTO extraUser;

} 