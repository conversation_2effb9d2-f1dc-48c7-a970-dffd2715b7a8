package cn.iocoder.yudao.module.wbclass.dal.dataobject.product;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 课程SKU关联 DO
 *
 * <AUTHOR>
 */
@TableName("edusys_wbclass_course_sku_relation")
@KeySequence("edusys_wbclass_course_sku_relation_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WbClassCourseSkuRelationDO extends BaseDO {

    /**
     * 关联ID
     */
    @TableId
    private Long id;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 排序
     */
    private Integer sort;

}
