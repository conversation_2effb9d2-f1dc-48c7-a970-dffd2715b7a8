# 课程订单表结构重构

## 背景

对 `edusys_wbclass_course_order` 表进行结构优化，主要目标：

1. **删除不必要字段**：移除 `sku_code`、`validity_start_time`、`validity_end_time` 等冗余字段
2. **优化字段顺序**：按业务逻辑重新排列字段，提高可读性和维护性
3. **完善索引策略**：添加必要的索引，提升查询性能

## 删除的字段

### 1. sku_code (SKU编码)
- **删除原因**：可以通过 `sku_id` 关联 `edusys_wbclass_course_product_sku` 表获取
- **影响**：减少数据冗余，避免数据不一致

### 2. validity_start_time (课程有效期开始时间)
- **删除原因**：业务中不需要单独管理课程有效期
- **影响**：简化订单模型

### 3. validity_end_time (课程有效期结束时间)
- **删除原因**：业务中不需要单独管理课程有效期
- **影响**：简化订单模型

## 优化后的字段顺序

### 1. 基础信息
- `id` - 订单ID
- `order_no` - 订单号

### 2. 用户信息
- `user_id` - 用户ID
- `user_ip` - 用户IP
- `terminal` - 下单终端

### 3. 商品信息（按重要性排序）
- `product_id` - 产品ID
- `sku_id` - SKU ID
- `sku_name` - SKU名称
- `course_id` - 主课程ID（向下兼容）
- `course_name` - 课程名称

### 4. 价格信息
- `original_price` - 原价
- `discount_price` - 优惠金额
- `pay_price` - 实付金额

### 5. 订单状态
- `status` - 订单状态
- `pay_status` - 支付状态
- `refund_status` - 退款状态

### 6. 支付信息
- `pay_time` - 支付时间
- `pay_order_id` - 支付订单ID
- `pay_channel` - 支付渠道

### 7. 退款信息
- `refund_price` - 退款金额
- `refund_time` - 退款时间
- `refund_reason` - 退款原因

### 8. 取消信息
- `cancel_time` - 取消时间
- `cancel_type` - 取消类型

### 9. 备注信息
- `user_remark` - 用户备注
- `admin_remark` - 管理员备注

### 10. 系统字段
- `creator` - 创建者
- `create_time` - 创建时间
- `updater` - 更新者
- `update_time` - 更新时间
- `deleted` - 是否删除
- `tenant_id` - 租户编号

## 索引优化

### 新增索引
- `idx_product_id` - 产品ID索引
- `idx_sku_id` - SKU ID索引
- `idx_product_sku` - 产品和SKU复合索引
- `idx_user_status` - 用户和状态复合索引
- `idx_user_pay_status` - 用户和支付状态复合索引
- `idx_create_time` - 创建时间索引

## 修改文件清单

### 数据库
- `order_table_restructure.sql` - 表结构重构脚本

### 实体类
- `WbClassCourseOrderDO.java` - 删除相关字段

### 服务层
- `WbClassCourseOrderServiceImpl.java` - 删除对 skuCode 的设置

### VO类
- `WbClassCourseOrderRespVO.java` - 删除相关字段
- `AppWbclassStudyRespVO.java` - 删除相关字段

## 执行步骤

### 1. 数据库重构
```bash
mysql -u your_username -p your_database < cursor-change-md/02-order-table-restructure/order_table_restructure.sql
```

### 2. 代码部署
- 重新编译和部署应用

### 3. 数据验证
- 验证数据迁移完整性
- 测试订单创建和查询功能

## 验证方法

### 1. 数据完整性验证
```sql
-- 检查记录数是否一致
SELECT 
  '原表记录数' as table_name, COUNT(*) as record_count 
FROM edusys_wbclass_course_order_backup
UNION ALL
SELECT 
  '新表记录数' as table_name, COUNT(*) as record_count 
FROM edusys_wbclass_course_order;

-- 检查关键字段数据
SELECT 
    COUNT(*) as total_orders,
    COUNT(product_id) as orders_with_product,
    COUNT(sku_id) as orders_with_sku
FROM edusys_wbclass_course_order;
```

### 2. 功能验证
- 创建新订单，验证数据正确性
- 测试订单查询和分页功能
- 测试手动分配订单功能

## 注意事项

1. **数据备份**：脚本会自动创建备份表 `edusys_wbclass_course_order_backup`
2. **分步执行**：建议先在测试环境验证，确认无误后再在生产环境执行
3. **索引影响**：新增索引可能会影响写入性能，但会显著提升查询性能
4. **向下兼容**：保留了 `course_id` 字段，确保现有功能不受影响

## 性能提升

1. **查询优化**：通过合理的字段顺序和索引，提升查询效率
2. **存储优化**：删除冗余字段，减少存储空间
3. **维护性提升**：清晰的字段分组，便于理解和维护

## 后续建议

1. **监控性能**：关注重构后的查询性能变化
2. **清理备份**：确认无误后，可以删除备份表
3. **文档更新**：更新相关的API文档和数据字典
