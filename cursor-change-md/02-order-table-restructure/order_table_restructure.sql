-- 课程订单表结构重构
-- 删除不必要字段，优化字段顺序

-- 1. 备份现有数据
CREATE TABLE edusys_wbclass_course_order_backup AS 
SELECT * FROM edusys_wbclass_course_order;

-- 2. 创建新的表结构（字段顺序优化）
CREATE TABLE edusys_wbclass_course_order_new (
  -- 基础信息
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  
  -- 用户信息
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_ip` varchar(64) DEFAULT NULL COMMENT '用户IP',
  `terminal` tinyint NOT NULL COMMENT '下单终端：1-PC，2-H5，3-小程序，4-APP',
  
  -- 商品信息（按重要性排序）
  `product_id` bigint DEFAULT NULL COMMENT '产品ID，关联 edusys_wbclass_course_product.id',
  `sku_id` bigint DEFAULT NULL COMMENT 'SKU ID，关联 edusys_wbclass_course_product_sku.id',
  `sku_name` varchar(255) DEFAULT NULL COMMENT 'SKU名称',
  `course_id` bigint NOT NULL COMMENT '主课程ID，关联 edusys_wbclass_course.id（用于向下兼容）',
  `course_name` varchar(255) NOT NULL COMMENT '课程名称',
  
  -- 价格信息
  `original_price` int NOT NULL DEFAULT '0' COMMENT '课程原价，单位：分',
  `discount_price` int NOT NULL DEFAULT '0' COMMENT '优惠金额，单位：分',
  `pay_price` int NOT NULL DEFAULT '0' COMMENT '实付金额，单位：分',
  
  -- 订单状态
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-已支付，3-已取消，4-已退款',
  `pay_status` tinyint NOT NULL DEFAULT '1' COMMENT '支付状态：1-未支付，2-已支付',
  `refund_status` tinyint NOT NULL DEFAULT '1' COMMENT '退款状态：1-无需退款，2-退款中，3-已退款',
  
  -- 支付信息
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `pay_order_id` bigint DEFAULT NULL COMMENT '支付订单ID',
  `pay_channel` tinyint DEFAULT NULL COMMENT '支付渠道',
  
  -- 退款信息
  `refund_price` int DEFAULT '0' COMMENT '退款金额，单位：分',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `refund_reason` varchar(500) DEFAULT NULL COMMENT '退款原因',
  
  -- 取消信息
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `cancel_type` tinyint DEFAULT NULL COMMENT '取消类型：1-用户取消，2-系统取消，3-超时取消',
  
  -- 备注信息
  `user_remark` varchar(500) DEFAULT NULL COMMENT '用户备注',
  `admin_remark` varchar(500) DEFAULT NULL COMMENT '管理员备注',
  
  -- 系统字段
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '3' COMMENT '租户编号',
  
  -- 主键和索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_product_sku` (`product_id`, `sku_id`),
  KEY `idx_status` (`status`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_user_status` (`user_id`, `status`),
  KEY `idx_user_pay_status` (`user_id`, `pay_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='练习营课程订单表';

-- 3. 迁移数据（排除删除的字段）
INSERT INTO edusys_wbclass_course_order_new (
  id, order_no, user_id, user_ip, terminal,
  product_id, sku_id, sku_name, course_id, course_name,
  original_price, discount_price, pay_price,
  status, pay_status, refund_status,
  pay_time, pay_order_id, pay_channel,
  refund_price, refund_time, refund_reason,
  cancel_time, cancel_type,
  user_remark, admin_remark,
  creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
  id, order_no, user_id, user_ip, terminal,
  product_id, sku_id, sku_name, course_id, course_name,
  original_price, discount_price, pay_price,
  status, pay_status, refund_status,
  pay_time, pay_order_id, pay_channel,
  refund_price, refund_time, refund_reason,
  cancel_time, cancel_type,
  user_remark, admin_remark,
  creator, create_time, updater, update_time, deleted, tenant_id
FROM edusys_wbclass_course_order;

-- 4. 验证数据迁移
SELECT 
  '原表记录数' as table_name, COUNT(*) as record_count 
FROM edusys_wbclass_course_order
UNION ALL
SELECT 
  '新表记录数' as table_name, COUNT(*) as record_count 
FROM edusys_wbclass_course_order_new;

-- 5. 替换表（请在确认数据无误后执行）
-- RENAME TABLE edusys_wbclass_course_order TO edusys_wbclass_course_order_old;
-- RENAME TABLE edusys_wbclass_course_order_new TO edusys_wbclass_course_order;

-- 6. 清理备份表（可选，建议保留一段时间）
-- DROP TABLE edusys_wbclass_course_order_backup;
-- DROP TABLE edusys_wbclass_course_order_old;
