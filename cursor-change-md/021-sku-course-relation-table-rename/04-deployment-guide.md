# SKU管理功能修复和表名重构部署指南

## 概述

本次更新包含两个主要修复：
1. **前端修复**：SKU管理弹窗中关联课程显示问题
2. **后端重构**：表名从 `edusys_wbclass_sku_course_relation` 改为 `edusys_wbclass_course_sku_relation`

## 部署前准备

### 1. 数据备份
```sql
-- 备份相关表数据
CREATE TABLE edusys_wbclass_sku_course_relation_backup AS 
SELECT * FROM edusys_wbclass_sku_course_relation;

CREATE TABLE edusys_wbclass_course_product_sku_backup AS 
SELECT * FROM edusys_wbclass_course_product_sku;
```

### 2. 环境检查
- 确认数据库连接正常
- 确认应用服务可以正常停止和启动
- 准备回滚方案

## 部署步骤

### 第一阶段：代码部署

1. **停止应用服务**
   ```bash
   # 停止应用
   systemctl stop your-app-service
   ```

2. **部署新代码**
   - 部署包含新DO类和Mapper的后端代码
   - 部署修复后的前端代码

3. **验证代码部署**
   - 检查新文件是否存在
   - 检查旧文件是否已删除

### 第二阶段：数据库迁移

1. **执行迁移脚本**
   ```bash
   mysql -u username -p database_name < 01-table-rename-migration.sql
   ```

2. **验证迁移结果**
   ```bash
   mysql -u username -p database_name < 03-verification-script.sql
   ```

3. **检查关键指标**
   - 新表是否创建成功
   - 数据是否完整迁移
   - 索引是否正确创建

### 第三阶段：应用启动和验证

1. **启动应用服务**
   ```bash
   systemctl start your-app-service
   ```

2. **检查应用日志**
   ```bash
   tail -f /path/to/application.log
   ```

3. **功能验证**
   - 访问课程商品管理页面
   - 测试SKU管理功能
   - 验证课程关联显示

## 验证清单

### 数据库验证
- [ ] 新表 `edusys_wbclass_course_sku_relation` 创建成功
- [ ] 旧表 `edusys_wbclass_sku_course_relation` 已删除（可选）
- [ ] 数据迁移完整，记录数一致
- [ ] 索引创建正确
- [ ] 无孤立数据记录

### 应用验证
- [ ] 应用启动无错误
- [ ] 无编译错误或类找不到的异常
- [ ] API接口正常响应

### 功能验证
- [ ] SKU管理弹窗正常打开
- [ ] SKU列表正确显示关联课程
- [ ] "管理课程"功能正常工作
- [ ] 课程关联保存功能正常
- [ ] 课程关联删除功能正常

## 回滚方案

如果部署过程中出现问题，可以按以下步骤回滚：

### 1. 代码回滚
```bash
# 停止应用
systemctl stop your-app-service

# 恢复旧版本代码
git checkout previous-version

# 重新部署
# ... 部署步骤

# 启动应用
systemctl start your-app-service
```

### 2. 数据库回滚
```sql
-- 如果新表已创建但有问题，可以删除新表
DROP TABLE IF EXISTS edusys_wbclass_course_sku_relation;

-- 如果旧表已删除，从备份恢复
CREATE TABLE edusys_wbclass_sku_course_relation AS 
SELECT * FROM edusys_wbclass_sku_course_relation_backup;
```

## 监控和观察

### 关键指标
- 应用启动时间
- API响应时间
- 错误日志数量
- 数据库连接状态

### 监控命令
```bash
# 检查应用状态
systemctl status your-app-service

# 监控应用日志
tail -f /path/to/application.log | grep -E "(ERROR|WARN|Exception)"

# 检查数据库连接
mysql -u username -p -e "SELECT 1"
```

## 常见问题和解决方案

### 1. 类找不到异常
**问题**：`ClassNotFoundException: WbClassSkuCourseRelationDO`
**解决**：确认旧文件已删除，新文件已正确部署

### 2. 表不存在异常
**问题**：`Table 'edusys_wbclass_course_sku_relation' doesn't exist`
**解决**：检查数据库迁移脚本是否执行成功

### 3. 前端课程不显示
**问题**：SKU管理弹窗中课程列表为空
**解决**：检查API调用是否使用了正确的接口

### 4. 数据不一致
**问题**：新表数据与旧表不一致
**解决**：重新执行数据迁移脚本，检查迁移逻辑

## 联系信息

如果在部署过程中遇到问题，请联系：
- 开发团队：[开发团队联系方式]
- 运维团队：[运维团队联系方式]
- 紧急联系：[紧急联系方式]

## 附录

### A. 相关文件清单
- `01-table-rename-migration.sql` - 数据库迁移脚本
- `02-code-changes-summary.md` - 代码修改总结
- `03-verification-script.sql` - 验证脚本
- `04-deployment-guide.md` - 本部署指南

### B. 测试数据
```sql
-- 用于测试的示例数据
INSERT INTO edusys_wbclass_course_sku_relation (sku_id, course_id, sort) 
VALUES (1, 1, 0), (1, 2, 1), (2, 1, 0);
```
