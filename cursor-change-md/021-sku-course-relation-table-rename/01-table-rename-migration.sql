-- ===================================================================
-- SKU课程关联表重命名迁移脚本
-- 将表名从 edusys_wbclass_sku_course_relation 改为 edusys_wbclass_course_sku_relation
-- ===================================================================

-- 1. 数据迁移前的准备工作
-- 检查现有数据状态
SELECT 
    COUNT(*) as total_relations,
    COUNT(DISTINCT sku_id) as unique_skus,
    COUNT(DISTINCT course_id) as unique_courses
FROM edusys_wbclass_sku_course_relation 
WHERE deleted = 0;

-- 检查是否存在重复的SKU-课程关联
SELECT 
    sku_id, 
    course_id, 
    COUNT(*) as count
FROM edusys_wbclass_sku_course_relation
WHERE deleted = 0
GROUP BY sku_id, course_id
HAVING COUNT(*) > 1;

-- 2. 创建新表结构
CREATE TABLE `edusys_wbclass_course_sku_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `sku_id` bigint NOT NULL COMMENT 'SKU ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `sort` int DEFAULT 0 COMMENT '排序',
  -- 基础字段
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_course` (`sku_id`, `course_id`, `deleted`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_course_id` (`course_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SKU课程关联表';

-- 3. 数据迁移
-- 从旧表迁移数据到新表
INSERT INTO edusys_wbclass_course_sku_relation (
    sku_id,
    course_id,
    sort,
    creator,
    create_time,
    updater,
    update_time,
    deleted,
    tenant_id
)
SELECT 
    sku_id,
    course_id,
    COALESCE(sort, 0) as sort,
    COALESCE(creator, 'migration') as creator,
    COALESCE(create_time, NOW()) as create_time,
    COALESCE(updater, 'migration') as updater,
    COALESCE(update_time, NOW()) as update_time,
    COALESCE(deleted, 0) as deleted,
    COALESCE(tenant_id, 0) as tenant_id
FROM edusys_wbclass_sku_course_relation
WHERE NOT EXISTS (
    SELECT 1 
    FROM edusys_wbclass_course_sku_relation new_table
    WHERE new_table.sku_id = edusys_wbclass_sku_course_relation.sku_id
      AND new_table.course_id = edusys_wbclass_sku_course_relation.course_id
      AND new_table.deleted = edusys_wbclass_sku_course_relation.deleted
);

-- 4. 数据迁移验证
-- 验证迁移结果
SELECT
    '旧表记录数' as description,
    COUNT(*) as count
FROM edusys_wbclass_sku_course_relation
WHERE deleted = 0

UNION ALL

SELECT
    '新表记录数' as description,
    COUNT(*) as count
FROM edusys_wbclass_course_sku_relation
WHERE deleted = 0;

-- 检查数据一致性
SELECT 
    old_table.sku_id,
    old_table.course_id,
    old_table.sort as old_sort,
    new_table.sort as new_sort,
    CASE 
        WHEN new_table.id IS NULL THEN '新表中缺失'
        WHEN old_table.sort != new_table.sort THEN '排序不一致'
        ELSE '正常'
    END as status
FROM edusys_wbclass_sku_course_relation old_table
LEFT JOIN edusys_wbclass_course_sku_relation new_table 
    ON old_table.sku_id = new_table.sku_id 
    AND old_table.course_id = new_table.course_id
    AND old_table.deleted = new_table.deleted
WHERE old_table.deleted = 0
  AND (new_table.id IS NULL OR old_table.sort != new_table.sort)
LIMIT 10;

-- 5. 清理旧表（谨慎执行）
-- 注意：只有在确认数据迁移成功且代码已更新后才执行此步骤
-- DROP TABLE IF EXISTS `edusys_wbclass_sku_course_relation`;

-- 6. 创建序列（如果使用Oracle或PostgreSQL）
-- 对于MySQL，AUTO_INCREMENT会自动处理，无需额外序列

-- 7. 权限和索引优化
-- 为新表创建必要的索引（已在CREATE TABLE中定义）
-- 如需要额外的复合索引，可在此添加

-- 8. 迁移完成后的验证查询
-- 验证SKU关联的课程数据
SELECT 
    s.id as sku_id,
    s.sku_name,
    COUNT(r.course_id) as course_count,
    GROUP_CONCAT(c.name ORDER BY r.sort, r.id) as course_names
FROM edusys_wbclass_course_product_sku s
LEFT JOIN edusys_wbclass_course_sku_relation r ON s.id = r.sku_id AND r.deleted = 0
LEFT JOIN edusys_wbclass_course c ON r.course_id = c.id AND c.deleted = 0
WHERE s.deleted = 0
GROUP BY s.id, s.sku_name
ORDER BY s.id
LIMIT 10;
