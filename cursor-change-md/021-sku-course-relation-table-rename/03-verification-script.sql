-- ===================================================================
-- SKU课程关联表重构验证脚本
-- 用于验证表名重构和功能修复是否成功
-- ===================================================================

-- 1. 验证新表是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'edusys_wbclass_course_sku_relation';

-- 2. 验证新表结构
DESCRIBE edusys_wbclass_course_sku_relation;

-- 3. 验证新表索引
SHOW INDEX FROM edusys_wbclass_course_sku_relation;

-- 4. 验证数据迁移完整性
-- 检查新表数据量
SELECT 
    '新表记录数' as description,
    COUNT(*) as count,
    COUNT(DISTINCT sku_id) as unique_skus,
    COUNT(DISTINCT course_id) as unique_courses
FROM edusys_wbclass_course_sku_relation
WHERE deleted = 0;

-- 5. 验证SKU关联的课程数据
-- 查看前10个SKU的课程关联情况
SELECT 
    s.id as sku_id,
    s.sku_name,
    s.product_id,
    COUNT(r.course_id) as course_count,
    GROUP_CONCAT(c.name ORDER BY r.sort, r.id SEPARATOR ', ') as course_names
FROM edusys_wbclass_course_product_sku s
LEFT JOIN edusys_wbclass_course_sku_relation r ON s.id = r.sku_id AND r.deleted = 0
LEFT JOIN edusys_wbclass_course c ON r.course_id = c.id AND c.deleted = 0
WHERE s.deleted = 0
GROUP BY s.id, s.sku_name, s.product_id
ORDER BY s.id
LIMIT 10;

-- 6. 验证课程关联的SKU数据
-- 查看前10个课程的SKU关联情况
SELECT 
    c.id as course_id,
    c.name as course_name,
    COUNT(r.sku_id) as sku_count,
    GROUP_CONCAT(s.sku_name ORDER BY r.sort, r.id SEPARATOR ', ') as sku_names
FROM edusys_wbclass_course c
LEFT JOIN edusys_wbclass_course_sku_relation r ON c.id = r.course_id AND r.deleted = 0
LEFT JOIN edusys_wbclass_course_product_sku s ON r.sku_id = s.id AND s.deleted = 0
WHERE c.deleted = 0
GROUP BY c.id, c.name
HAVING sku_count > 0
ORDER BY c.id
LIMIT 10;

-- 7. 验证数据完整性
-- 检查是否有孤立的关联记录（SKU不存在）
SELECT 
    r.id as relation_id,
    r.sku_id,
    r.course_id,
    'SKU不存在' as issue
FROM edusys_wbclass_course_sku_relation r
LEFT JOIN edusys_wbclass_course_product_sku s ON r.sku_id = s.id
WHERE r.deleted = 0 
  AND s.id IS NULL
LIMIT 5;

-- 检查是否有孤立的关联记录（课程不存在）
SELECT 
    r.id as relation_id,
    r.sku_id,
    r.course_id,
    '课程不存在' as issue
FROM edusys_wbclass_course_sku_relation r
LEFT JOIN edusys_wbclass_course c ON r.course_id = c.id
WHERE r.deleted = 0 
  AND c.id IS NULL
LIMIT 5;

-- 8. 验证排序功能
-- 检查同一SKU下课程的排序是否正确
SELECT 
    r.sku_id,
    r.course_id,
    c.name as course_name,
    r.sort,
    ROW_NUMBER() OVER (PARTITION BY r.sku_id ORDER BY r.sort, r.id) as expected_order
FROM edusys_wbclass_course_sku_relation r
INNER JOIN edusys_wbclass_course c ON r.course_id = c.id
WHERE r.deleted = 0
  AND r.sku_id IN (
      SELECT sku_id 
      FROM edusys_wbclass_course_sku_relation 
      WHERE deleted = 0 
      GROUP BY sku_id 
      HAVING COUNT(*) > 1 
      LIMIT 3
  )
ORDER BY r.sku_id, r.sort, r.id;

-- 9. 性能验证
-- 验证索引是否生效（查看执行计划）
EXPLAIN SELECT * FROM edusys_wbclass_course_sku_relation WHERE sku_id = 1 AND deleted = 0;
EXPLAIN SELECT * FROM edusys_wbclass_course_sku_relation WHERE course_id = 1 AND deleted = 0;

-- 10. 功能验证查询
-- 模拟API查询：根据SKU ID获取关联的课程列表
SELECT 
    c.id,
    c.name,
    c.description,
    c.status,
    r.sort
FROM edusys_wbclass_course_sku_relation r
INNER JOIN edusys_wbclass_course c ON r.course_id = c.id
WHERE r.sku_id = 1  -- 替换为实际的SKU ID
  AND r.deleted = 0
  AND c.deleted = 0
ORDER BY r.sort, r.id;

-- 模拟API查询：根据课程ID获取关联的SKU列表
SELECT 
    s.id,
    s.sku_name,
    s.original_price,
    s.sale_price,
    s.stock,
    s.status,
    r.sort
FROM edusys_wbclass_course_sku_relation r
INNER JOIN edusys_wbclass_course_product_sku s ON r.sku_id = s.id
WHERE r.course_id = 1  -- 替换为实际的课程ID
  AND r.deleted = 0
  AND s.deleted = 0
ORDER BY r.sort, r.id;

-- 11. 检查旧表是否还存在（应该不存在）
SELECT 
    TABLE_NAME,
    TABLE_COMMENT
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'edusys_wbclass_sku_course_relation';

-- 如果上面的查询返回结果，说明旧表还存在，需要手动删除
-- DROP TABLE IF EXISTS `edusys_wbclass_sku_course_relation`;

-- 12. 最终验证总结
SELECT 
    '验证项目' as item,
    '状态' as status,
    '说明' as description
UNION ALL
SELECT '新表创建', 
       CASE WHEN EXISTS(SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'edusys_wbclass_course_sku_relation') 
            THEN '✓ 成功' ELSE '✗ 失败' END,
       '新表edusys_wbclass_course_sku_relation是否存在'
UNION ALL
SELECT '旧表清理',
       CASE WHEN NOT EXISTS(SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'edusys_wbclass_sku_course_relation') 
            THEN '✓ 成功' ELSE '✗ 失败' END,
       '旧表edusys_wbclass_sku_course_relation是否已删除'
UNION ALL
SELECT '数据迁移',
       CASE WHEN (SELECT COUNT(*) FROM edusys_wbclass_course_sku_relation WHERE deleted = 0) > 0 
            THEN '✓ 成功' ELSE '⚠ 检查' END,
       '新表是否包含数据';
