# SKU管理功能修复和表名重构总结

## 问题描述

1. **SKU管理弹窗中的关联课程显示问题**：在SKU管理弹窗中，点击"管理课程"功能后，关联课程没有正确显示出来。

2. **表名重构需求**：需要将表名从 `edusys_wbclass_sku_course_relation` 改为 `edusys_wbclass_course_sku_relation`，以符合命名约定（course在sku之前）。

## 解决方案

### 1. 前端修复 - SKU关联课程显示问题

**问题根因**：前端调用了错误的API，使用了获取商品详情的API而不是专门的SKU列表API。

**修改文件**：
- `yudao-ui-admin/src/views/wbclass/courseProduct/components/SkuManagement.vue`

**具体修改**：
1. 添加了 `getWbclassCourseProductSkuList` 的import
2. 修改 `loadSkuData()` 方法，使用正确的API调用：
   ```javascript
   // 修改前
   const response = await getWbclassCourseProduct(this.productId);
   this.skuList = response.data.skus || [];
   
   // 修改后
   const response = await getWbclassCourseProductSkuList(this.productId);
   this.skuList = response.data || [];
   ```

**效果**：现在SKU列表会正确显示每个SKU关联的课程信息。

### 2. 后端重构 - 表名和类名修改

**数据库层面**：
1. 创建了数据库迁移脚本：`01-table-rename-migration.sql`
2. 新表名：`edusys_wbclass_course_sku_relation`
3. 包含完整的数据迁移和验证逻辑

**代码层面修改**：

#### 新增文件：
1. `WbClassCourseSkuRelationDO.java` - 新的DO类
2. `WbClassCourseSkuRelationMapper.java` - 新的Mapper接口

#### 修改文件：
1. `WbClassSkuCourseRelationDO.java` - 更新@TableName注解
2. `WbClassCourseProductSkuServiceImpl.java` - 更新import和类型引用

**具体修改内容**：

1. **DO类更新**：
   ```java
   // 修改前
   @TableName("edusys_wbclass_sku_course_relation")
   @KeySequence("edusys_wbclass_sku_course_relation_seq")
   
   // 修改后
   @TableName("edusys_wbclass_course_sku_relation")
   @KeySequence("edusys_wbclass_course_sku_relation_seq")
   ```

2. **Service类更新**：
   - 更新import语句
   - 更新Mapper字段类型
   - 更新方法中的DO类引用

## 迁移步骤

### 数据库迁移
1. 执行 `01-table-rename-migration.sql` 脚本
2. 验证数据迁移完整性
3. 确认新表数据正确后，删除旧表

### 代码部署
1. 部署包含新DO类和Mapper的代码
2. 验证功能正常工作
3. 清理旧的DO类和Mapper文件（可选）

## 验证方法

### 前端验证
1. 打开课程商品管理页面
2. 点击某个商品的"SKU管理"按钮
3. 在SKU列表中查看"关联课程"列是否正确显示课程信息
4. 点击"管理课程"按钮，验证课程关联功能是否正常

### 后端验证
1. 检查新表是否创建成功
2. 验证数据是否正确迁移
3. 测试SKU课程关联的增删改查功能
4. 确认API返回的数据包含正确的课程信息

## 注意事项

1. **数据备份**：在执行数据库迁移前，请务必备份相关数据
2. **分步部署**：建议先部署代码，再执行数据库迁移
3. **回滚方案**：保留旧表和旧代码，以便必要时回滚
4. **测试验证**：在生产环境部署前，请在测试环境充分验证

## 影响范围

- 前端：SKU管理组件的课程显示功能
- 后端：SKU课程关联相关的所有功能
- 数据库：新增表，数据迁移

## 兼容性

- 新代码向后兼容，支持新表结构
- 迁移过程中可能需要短暂的服务中断
- 建议在低峰期执行迁移操作
