# 订单支付成功后课程分配逻辑重构方案

## 项目概述

本项目旨在重构订单支付成功后的课程分配逻辑，解决当前系统中订单与课程耦合的问题，通过引入独立的关联机制来管理订单与课程的关系。

## 问题分析

### 现有系统问题

1. **数据耦合**：订单表直接存储课程信息（courseId, courseName），造成数据冗余
2. **功能限制**：一个订单只能关联一个主课程，但实际上一个 SKU 可能包含多个课程
3. **查询复杂**：用户查询已购课程需要直接查询订单表，逻辑不够清晰
4. **维护困难**：课程信息变更时需要同步更新订单表中的冗余数据

### 重构目标

1. **解耦设计**：将订单与课程的关联关系独立管理
2. **支持多课程**：一个订单可以关联多个课程
3. **查询优化**：提供清晰的用户已购课程查询接口
4. **数据一致性**：保持数据的完整性和一致性
5. **向后兼容**：确保现有功能不受影响

## 解决方案

### 1. 数据库设计

#### 新增订单课程关联表

```sql
CREATE TABLE `edusys_wbclass_course_order_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `course_name` varchar(255) NOT NULL COMMENT '课程名称（冗余字段，便于查询）',
  `sort` int DEFAULT 0 COMMENT '排序（同一订单下课程的排序）',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-正常，2-已删除',
  -- 其他标准字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_course` (`order_id`, `course_id`, `deleted`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_course_id` (`course_id`)
);
```

#### 设计特点

- **一对多关系**：支持一个订单关联多个课程
- **排序支持**：通过 sort 字段支持课程排序
- **冗余优化**：存储课程名称减少 JOIN 查询
- **软删除**：支持数据恢复和历史追踪
- **唯一约束**：防止重复关联
- **索引优化**：针对常见查询场景优化

### 2. 代码架构

#### 新增服务层

- **WbClassOrderCourseRelationService**：订单课程关联业务逻辑
- **WbClassOrderCourseRelationServiceImpl**：具体实现

#### 数据访问层

- **WbClassOrderCourseRelationDO**：关联表实体
- **WbClassOrderCourseRelationMapper**：数据访问接口

#### 核心功能

1. **创建关联**：支付成功后创建订单-课程关联
2. **查询课程**：根据订单或用户查询已购课程
3. **删除关联**：退款时删除关联关系
4. **兼容处理**：向后兼容现有数据

### 3. 业务流程重构

#### 订单支付成功流程

```
1. 更新订单状态为已支付
2. 增加SKU销量
3. 创建订单-课程关联记录 ← 新增
4. 初始化用户学习进度（基于关联表）← 修改
```

#### 学习进度初始化流程

```
1. 优先从订单-课程关联表获取课程列表 ← 新增
2. 如果关联表无数据，回退到SKU查询（向后兼容）
3. 为每个课程初始化课节和练习进度
```

#### 用户课程查询流程

```
1. 查询用户已支付且未退款的订单
2. 通过订单-课程关联表获取课程列表 ← 修改
3. 返回去重后的课程信息
```

## 实施计划

### 阶段 1：基础设施建设

- [x] 设计数据库表结构
- [x] 创建数据访问层（DO、Mapper）
- [x] 实现订单课程关联服务

### 阶段 2：业务逻辑重构

- [x] 重构订单支付成功逻辑
- [x] 更新学习进度初始化逻辑
- [x] 修改用户课程查询逻辑

### 阶段 3：数据迁移

- [x] 设计数据迁移脚本
- [ ] 在测试环境验证迁移脚本
- [ ] 在生产环境执行数据迁移

### 阶段 4：测试验证

- [x] 编写单元测试
- [x] 制定测试验证指南
- [ ] 执行功能测试
- [ ] 执行性能测试

### 阶段 5：部署上线

- [ ] 代码部署
- [ ] 数据迁移
- [ ] 监控观察
- [ ] 问题修复

## 技术亮点

### 1. 向后兼容设计

- 保留原有订单表结构，不破坏现有功能
- 学习进度初始化支持新旧数据源切换
- 渐进式迁移，降低风险

### 2. 性能优化

- 合理的索引设计优化查询性能
- 冗余存储课程名称减少 JOIN 操作
- 分页查询支持大数据量场景

### 3. 数据一致性

- 事务保证数据操作的原子性
- 软删除机制支持数据恢复
- 唯一约束防止数据重复

### 4. 扩展性设计

- 支持一个订单关联多个课程
- 排序字段支持课程顺序管理
- 状态字段支持业务状态扩展

## 风险控制

### 1. 数据风险

- **备份策略**：执行前完整备份相关数据
- **回滚方案**：准备数据回滚脚本
- **验证机制**：多层次数据验证

### 2. 功能风险

- **兼容性测试**：确保现有功能不受影响
- **渐进部署**：分步骤部署降低风险
- **监控告警**：实时监控系统状态

### 3. 性能风险

- **性能测试**：验证新方案的性能表现
- **索引优化**：确保查询性能不下降
- **容量规划**：评估存储和计算资源需求

## 预期收益

### 1. 架构优化

- 解耦订单与课程的直接关联
- 提高系统的可维护性和扩展性
- 简化业务逻辑，提高代码质量

### 2. 功能增强

- 支持一个订单包含多个课程
- 提供更灵活的课程管理能力
- 优化用户课程查询体验

### 3. 数据治理

- 减少数据冗余，提高数据一致性
- 建立清晰的数据关联关系
- 便于后续数据分析和报表生成

## 后续规划

### 1. 功能扩展

- 支持课程包和套餐管理
- 实现更复杂的课程权限控制
- 添加课程学习路径功能

### 2. 性能优化

- 引入缓存机制提高查询性能
- 优化数据库查询和索引
- 考虑读写分离架构

### 3. 数据分析

- 基于关联表进行用户行为分析
- 生成课程销售和学习报表
- 支持个性化推荐功能

## 总结

本次重构通过引入订单课程关联表，成功解决了订单与课程耦合的问题，实现了：

1. **架构解耦**：订单和课程通过独立的关联表管理关系
2. **功能增强**：支持一个订单关联多个课程
3. **性能优化**：通过合理的索引和冗余设计提高查询性能
4. **向后兼容**：确保现有功能不受影响
5. **数据一致性**：建立了清晰的数据关联关系

该方案不仅解决了当前的技术债务，还为未来的功能扩展奠定了良好的基础。通过完善的测试验证和风险控制措施，确保了重构的安全性和可靠性。
