-- ===================================================================
-- 订单课程关联数据迁移脚本
-- 将现有订单表中的课程关联关系迁移到新的关联表中
-- ===================================================================

-- 1. 数据迁移前的准备工作
-- 检查现有数据状态
SELECT 
    COUNT(*) as total_orders,
    COUNT(CASE WHEN course_id IS NOT NULL THEN 1 END) as orders_with_course,
    COUNT(CASE WHEN sku_id IS NOT NULL THEN 1 END) as orders_with_sku
FROM edusys_wbclass_course_order 
WHERE deleted = 0;

-- 检查是否存在重复的订单-课程关联
SELECT 
    order_id, 
    course_id, 
    COUNT(*) as count
FROM (
    SELECT 
        id as order_id,
        course_id
    FROM edusys_wbclass_course_order 
    WHERE deleted = 0 AND course_id IS NOT NULL
) t
GROUP BY order_id, course_id
HAVING COUNT(*) > 1;

-- 2. 执行数据迁移
-- 迁移策略1：直接从订单表迁移课程关联关系
INSERT INTO edusys_wbclass_course_order_relation (
    order_id,
    course_id,
    course_name,
    sort,
    status,
    creator,
    create_time,
    updater,
    update_time,
    deleted,
    tenant_id
)
SELECT 
    o.id as order_id,
    o.course_id,
    o.course_name,
    0 as sort,
    1 as status,
    'migration' as creator,
    NOW() as create_time,
    'migration' as updater,
    NOW() as update_time,
    0 as deleted,
    COALESCE(o.tenant_id, 0) as tenant_id
FROM edusys_wbclass_course_order o
WHERE o.deleted = 0 
  AND o.course_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 
      FROM edusys_wbclass_course_order_relation r
      WHERE r.order_id = o.id
        AND r.course_id = o.course_id
        AND r.deleted = 0
  );

-- 迁移策略2：基于SKU关联的课程进行迁移（处理多课程关联的情况）
-- 这个脚本需要根据实际的SKU-课程关联表来执行
INSERT INTO edusys_wbclass_course_order_relation (
    order_id,
    course_id,
    course_name,
    sort,
    status,
    creator,
    create_time,
    updater,
    update_time,
    deleted,
    tenant_id
)
SELECT 
    o.id as order_id,
    c.id as course_id,
    c.name as course_name,
    COALESCE(scr.sort, 0) as sort,
    1 as status,
    'migration' as creator,
    NOW() as create_time,
    'migration' as updater,
    NOW() as update_time,
    0 as deleted,
    COALESCE(o.tenant_id, 0) as tenant_id
FROM edusys_wbclass_course_order o
INNER JOIN edusys_wbclass_sku_course_relation scr ON o.sku_id = scr.sku_id
INNER JOIN edusys_wbclass_course c ON scr.course_id = c.id
WHERE o.deleted = 0 
  AND o.sku_id IS NOT NULL
  AND scr.deleted = 0
  AND c.deleted = 0
  AND NOT EXISTS (
      SELECT 1 
      FROM edusys_wbclass_course_order_relation r
      WHERE r.order_id = o.id
        AND r.course_id = c.id
        AND r.deleted = 0
  );

-- 3. 数据迁移后的验证
-- 验证迁移结果
SELECT
    '迁移前订单数' as description,
    COUNT(*) as count
FROM edusys_wbclass_course_order
WHERE deleted = 0 AND (course_id IS NOT NULL OR sku_id IS NOT NULL)

UNION ALL

SELECT
    '迁移后关联记录数' as description,
    COUNT(*) as count
FROM edusys_wbclass_course_order_relation
WHERE deleted = 0;

-- 检查是否有订单没有对应的课程关联
SELECT 
    o.id as order_id,
    o.order_no,
    o.course_id,
    o.sku_id,
    o.course_name
FROM edusys_wbclass_course_order o
LEFT JOIN edusys_wbclass_course_order_relation r ON o.id = r.order_id AND r.deleted = 0
WHERE o.deleted = 0 
  AND (o.course_id IS NOT NULL OR o.sku_id IS NOT NULL)
  AND r.id IS NULL
LIMIT 10;

-- 检查关联表中的数据完整性
SELECT 
    r.order_id,
    r.course_id,
    r.course_name,
    c.name as actual_course_name,
    CASE WHEN c.id IS NULL THEN '课程不存在' 
         WHEN r.course_name != c.name THEN '课程名称不匹配' 
         ELSE '正常' END as status
FROM edusys_wbclass_course_order_relation r
LEFT JOIN edusys_wbclass_course c ON r.course_id = c.id AND c.deleted = 0
WHERE r.deleted = 0
  AND (c.id IS NULL OR r.course_name != c.name)
LIMIT 10;

-- 4. 清理和优化建议
-- 注意：以下操作需要在确认迁移成功后谨慎执行

-- 可选：为关联表添加额外的索引以优化查询性能
-- CREATE INDEX idx_order_course_relation_user_course ON edusys_wbclass_course_order_relation (order_id, course_id);

-- 可选：更新课程名称不匹配的记录
-- UPDATE edusys_wbclass_course_order_relation r
-- INNER JOIN edusys_wbclass_course c ON r.course_id = c.id
-- SET r.course_name = c.name, r.update_time = NOW(), r.updater = 'migration_fix'
-- WHERE r.deleted = 0 AND c.deleted = 0 AND r.course_name != c.name;

-- ===================================================================
-- 迁移脚本使用说明
-- ===================================================================
-- 1. 在生产环境执行前，请先在测试环境验证脚本的正确性
-- 2. 建议在业务低峰期执行迁移脚本
-- 3. 执行前请备份相关数据表
-- 4. 分步骤执行，每个步骤后检查结果
-- 5. 迁移完成后，监控新功能的运行状况
-- 6. 确认新功能稳定运行一段时间后，可考虑清理订单表中的冗余字段

-- 回滚方案（如果需要）：
-- DELETE FROM edusys_wbclass_course_order_relation WHERE creator = 'migration';
