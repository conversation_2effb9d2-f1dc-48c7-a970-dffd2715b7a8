# 订单课程关联重构 - 测试验证指南

## 概述

本文档提供了订单课程关联重构后的测试验证指南，确保新功能正常工作且与现有系统兼容。

## 测试环境准备

### 1. 数据库准备

```sql
-- 1. 创建订单课程关联表
-- 执行 cursor-change-md/01-order-course-relation/01-database-design.sql

-- 2. 准备测试数据
-- 创建测试课程
INSERT INTO edusys_wbclass_course (name, status, creator, create_time, updater, update_time)
VALUES ('Java基础课程', 1, 'test', NOW(), 'test', NOW());

-- 创建测试商品和SKU
INSERT INTO edusys_wbclass_course_product (name, status, creator, create_time, updater, update_time)
VALUES ('Java学习套餐', 1, 'test', NOW(), 'test', NOW());

INSERT INTO edusys_wbclass_course_product_sku (product_id, sku_name, original_price, sale_price, status, creator, create_time, updater, update_time)
VALUES (1, 'Java基础版', 19900, 9900, 1, 'test', NOW(), 'test', NOW());

-- 创建SKU-课程关联
INSERT INTO edusys_wbclass_sku_course_relation (sku_id, course_id, sort, creator, create_time, updater, update_time)
VALUES (1, 1, 0, 'test', NOW(), 'test', NOW());
```

### 2. 运行单元测试

```bash
# 运行订单课程关联服务测试
mvn test -Dtest=WbClassOrderCourseRelationServiceTest

# 运行所有相关测试
mvn test -Dtest="*OrderCourse*"
```

## 功能测试场景

### 1. 订单支付成功测试

#### 测试步骤：

1. 创建一个包含 SKU 的订单
2. 模拟支付成功回调
3. 验证订单课程关联记录是否正确创建
4. 验证学习进度是否正确初始化

#### 验证 SQL：

```sql
-- 检查订单课程关联记录
SELECT * FROM edusys_wbclass_course_order_relation
WHERE order_id = ? AND deleted = 0;

-- 检查学习进度记录
SELECT * FROM edusys_wbclass_user_lesson_progress
WHERE order_id = ? AND member_id = ?;
```

#### 预期结果：

- 订单课程关联表中应该有对应的记录
- 每个关联的课程都应该有对应的学习进度记录
- 关联记录的课程名称应该与实际课程名称一致

### 2. 手动订单分配测试

#### 测试步骤：

1. 通过管理后台手动分配订单
2. 验证订单课程关联记录是否正确创建
3. 验证用户能否正常访问分配的课程

#### API 测试：

```bash
# 手动分配订单
curl -X POST "http://localhost:8080/admin-api/wbclass/course-order/assign" \
  -H "Content-Type: application/json" \
  -d '{
    "userMobile": "13800138000",
    "productId": 1,
    "skuId": 1,
    "payPrice": 9900,
    "userRemark": "测试分配"
  }'
```

### 3. 用户课程查询测试

#### 测试步骤：

1. 用户登录 APP
2. 调用获取学习数据接口
3. 验证返回的课程列表是否正确

#### API 测试：

```bash
# 获取用户学习数据
curl -X GET "http://localhost:8080/app-api/edusys/wbclass-study/get-my-study-data" \
  -H "Authorization: Bearer {token}"
```

#### 预期结果：

- 返回用户已购买的所有课程
- 课程信息应该来自订单课程关联表而不是直接从订单表
- 课程列表应该去重（同一课程的多个 SKU 只显示一次）

### 4. 学习进度初始化测试

#### 测试步骤：

1. 创建包含多个课程的 SKU 订单
2. 支付成功后检查学习进度初始化
3. 验证每个课程的课节和练习进度都正确创建

#### 验证 SQL：

```sql
-- 检查课节进度
SELECT
    ulp.course_id,
    c.name as course_name,
    COUNT(ulp.id) as lesson_count
FROM edusys_wbclass_user_lesson_progress ulp
JOIN edusys_wbclass_course c ON ulp.course_id = c.id
WHERE ulp.order_id = ? AND ulp.member_id = ?
GROUP BY ulp.course_id, c.name;

-- 检查练习进度
SELECT
    uep.course_id,
    c.name as course_name,
    COUNT(uep.id) as exercise_count
FROM edusys_wbclass_user_exercise_progress uep
JOIN edusys_wbclass_course c ON uep.course_id = c.id
WHERE uep.order_id = ? AND uep.member_id = ?
GROUP BY uep.course_id, c.name;
```

### 5. 订单退款测试

#### 测试步骤：

1. 创建已支付的订单
2. 执行退款操作
3. 验证订单课程关联记录是否被正确删除（软删除）
4. 验证用户无法再访问退款订单的课程

#### 验证 SQL：

```sql
-- 检查关联记录是否被软删除
SELECT * FROM edusys_wbclass_course_order_relation
WHERE order_id = ? AND status = 2; -- 2表示已删除
```

## 兼容性测试

### 1. 向后兼容测试

#### 测试场景：

- 现有订单（迁移前的数据）应该能正常工作
- 学习进度初始化应该能处理没有关联记录的情况
- 用户课程查询应该能同时处理新旧数据

#### 测试方法：

1. 不执行数据迁移脚本
2. 创建新订单并支付
3. 验证系统能否正常工作（应该回退到 SKU 查询模式）

### 2. 数据迁移测试

#### 测试步骤：

1. 准备包含各种情况的测试数据
2. 执行数据迁移脚本
3. 验证迁移结果的正确性
4. 测试迁移后的功能是否正常

#### 验证要点：

- 所有有效订单都应该有对应的课程关联记录
- 课程名称应该与实际课程名称一致
- 基于 SKU 的多课程关联应该正确迁移
- 迁移后的数据应该能正常支持新功能

## 性能测试

### 1. 查询性能测试

#### 测试场景：

- 大量订单数据下的用户课程查询性能
- 订单课程关联表的查询性能
- 学习进度初始化的性能

#### 测试方法：

```sql
-- 创建大量测试数据
-- 执行性能测试查询
-- 监控查询执行时间和资源使用情况
```

### 2. 并发测试

#### 测试场景：

- 多用户同时支付订单
- 并发创建订单课程关联记录
- 并发初始化学习进度

## 回归测试

### 1. 核心功能回归

- 订单创建流程
- 支付成功流程
- 用户学习数据查询
- 学习进度管理

### 2. 边界情况测试

- 空 SKU 的订单处理
- 无关联课程的 SKU 处理
- 重复支付处理
- 异常数据处理

## 监控和告警

### 1. 关键指标监控

- 订单课程关联创建成功率
- 学习进度初始化成功率
- 用户课程查询响应时间
- 数据一致性检查

### 2. 异常告警

- 关联记录创建失败
- 学习进度初始化失败
- 数据不一致情况
- 性能异常

## 测试清单

- [ ] 单元测试通过
- [ ] 订单支付成功流程测试
- [ ] 手动订单分配测试
- [ ] 用户课程查询测试
- [ ] 学习进度初始化测试
- [ ] 订单退款测试
- [ ] 向后兼容性测试
- [ ] 数据迁移测试
- [ ] 性能测试
- [ ] 并发测试
- [ ] 回归测试
- [ ] 监控告警配置

## 注意事项

1. **数据备份**：在生产环境执行任何操作前，务必备份相关数据
2. **分步部署**：建议分步骤部署，先部署代码，再执行数据迁移
3. **监控观察**：部署后密切监控系统运行状况
4. **回滚准备**：准备好回滚方案，以防出现问题
5. **用户通知**：如有必要，提前通知用户可能的服务中断
