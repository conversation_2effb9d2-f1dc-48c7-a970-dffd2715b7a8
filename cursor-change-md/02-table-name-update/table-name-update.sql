-- ===================================================================
-- 表名更新说明
-- 将 edusys_wbclass_order_course_relation 更改为 edusys_wbclass_course_order_relation
-- ===================================================================

-- 注意：本文件仅用于说明表名的更改，实际的表创建请使用 01-database-design.sql

-- 原表名（已废弃）：
-- edusys_wbclass_order_course_relation

-- 新表名（推荐使用）：
-- edusys_wbclass_course_order_relation

-- 更改原因：
-- 1. 遵循数据库命名约定，将主要实体（course）放在前面
-- 2. 与用户记忆中的命名习惯保持一致
-- 3. 符合项目中其他表的命名规范

-- 如果已经创建了旧表名，可以使用以下SQL进行重命名：
-- RENAME TABLE edusys_wbclass_order_course_relation TO edusys_wbclass_course_order_relation;

-- 相关文件已更新：
-- 1. 01-database-design.sql - 数据库表结构设计
-- 2. 02-data-migration.sql - 数据迁移脚本
-- 3. WbClassOrderCourseRelationDO.java - 实体类
-- 4. 03-testing-guide.md - 测试指南
-- 5. 00-refactoring-summary.md - 重构总结

-- 注意事项：
-- 1. 如果已经在测试环境创建了旧表名，请先重命名或删除重建
-- 2. 确保所有相关代码都使用新的表名
-- 3. 更新任何可能引用旧表名的文档或配置
